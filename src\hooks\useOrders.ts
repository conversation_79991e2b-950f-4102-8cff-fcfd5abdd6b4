
import { useState, useEffect } from 'react';
import { orderService } from '@/services/orderService';
import { OrderWithItems } from '@/types/order';
import { useToast } from '@/hooks/use-toast';

export const useOrders = () => {
  const [orders, setOrders] = useState<OrderWithItems[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  const fetchOrders = async () => {
    try {
      setLoading(true);
      setError(null);
      const { data, error } = await orderService.getAllOrders();
      
      if (error) throw error;
      setOrders(data || []);
    } catch (err: any) {
      setError(err.message || 'Failed to fetch orders');
      toast({
        title: "Error",
        description: "Failed to load orders",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchOrders();
  }, []);

  const refreshOrders = () => {
    fetchOrders();
  };

  return {
    orders,
    loading,
    error,
    refreshOrders,
  };
};
