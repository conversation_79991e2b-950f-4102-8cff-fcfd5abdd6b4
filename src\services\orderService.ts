
import { supabase } from '@/integrations/supabase/client';
import { Order, OrderWithItems, OrderStatus } from '@/types/order';

export const orderService = {
  async getAllOrders(): Promise<{ data: OrderWithItems[] | null; error: any }> {
    try {
      const { data, error } = await supabase
        .from('orders')
        .select(`
          *,
          profiles (
            email, full_name
          ),
          order_items (
            *,
            products (
              id, name, image_url, price
            )
          )
        `)
        .order('created_at', { ascending: false });
      
      return { data, error };
    } catch (error) {
      console.error('Error fetching all orders:', error);
      return { data: null, error };
    }
  },

  async getOrderById(id: string): Promise<{ data: OrderWithItems | null; error: any }> {
    try {
      const { data, error } = await supabase
        .from('orders')
        .select(`
          *,
          profiles (
            email, full_name
          ),
          order_items (
            *,
            products (
              id, name, image_url, price
            )
          )
        `)
        .eq('id', id)
        .single();
      
      return { data, error };
    } catch (error) {
      console.error('Error fetching order by ID:', error);
      return { data: null, error };
    }
  },

  async updateOrderStatus(id: string, status: OrderStatus): Promise<{ data: Order | null; error: any }> {
    try {
      const { data, error } = await supabase
        .from('orders')
        .update({ 
          status,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .select()
        .single();
      
      return { data, error };
    } catch (error) {
      console.error('Error updating order status:', error);
      return { data: null, error };
    }
  },

  async cancelOrder(id: string): Promise<{ data: Order | null; error: any }> {
    try {
      const { data, error } = await supabase
        .from('orders')
        .update({ 
          status: 'cancelled' as OrderStatus,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .select()
        .single();
      
      return { data, error };
    } catch (error) {
      console.error('Error cancelling order:', error);
      return { data: null, error };
    }
  },

  async getOrdersByStatus(status: OrderStatus): Promise<{ data: OrderWithItems[] | null; error: any }> {
    try {
      const { data, error } = await supabase
        .from('orders')
        .select(`
          *,
          profiles (
            email, full_name
          ),
          order_items (
            *,
            products (
              id, name, image_url, price
            )
          )
        `)
        .eq('status', status)
        .order('created_at', { ascending: false });
      
      return { data, error };
    } catch (error) {
      console.error('Error fetching orders by status:', error);
      return { data: null, error };
    }
  },

  async getOrderStats(): Promise<{ data: any; error: any }> {
    try {
      const { data, error } = await supabase
        .from('orders')
        .select('status, total_amount, created_at');
      
      if (error) return { data: null, error };

      const stats = {
        total: data.length,
        pending: data.filter(o => o.status === 'pending').length,
        processing: data.filter(o => o.status === 'processing').length,
        shipped: data.filter(o => o.status === 'shipped').length,
        delivered: data.filter(o => o.status === 'delivered').length,
        cancelled: data.filter(o => o.status === 'cancelled').length,
        totalRevenue: data
          .filter(o => o.status === 'delivered')
          .reduce((sum, o) => sum + o.total_amount, 0)
      };

      return { data: stats, error: null };
    } catch (error) {
      console.error('Error fetching order stats:', error);
      return { data: null, error };
    }
  }
};
