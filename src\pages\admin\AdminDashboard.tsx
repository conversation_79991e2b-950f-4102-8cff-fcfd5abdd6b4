
import React, { useState, useEffect } from 'react';
import { Navigate } from 'react-router-dom';
import { ShoppingBag, Package, Users, DollarSign, TrendingUp, AlertTriangle } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useAuth } from '@/contexts/AuthContext';
import { Layout } from '@/components/layout/Layout';
import { productService } from '@/services/productService';
import { orderService } from '@/services/orderService';
import { useToast } from '@/hooks/use-toast';

export default function AdminDashboard() {
  const { profile } = useAuth();
  const { toast } = useToast();
  const [stats, setStats] = useState({
    totalProducts: 0,
    activeProducts: 0,
    lowStockProducts: 0,
    totalOrders: 0,
    pendingOrders: 0,
    totalRevenue: 0,
    monthlyRevenue: 0
  });
  const [loading, setLoading] = useState(true);

  if (!profile?.is_admin) {
    return <Navigate to="/" replace />;
  }

  useEffect(() => {
    fetchDashboardStats();
  }, []);

  const fetchDashboardStats = async () => {
    try {
      setLoading(true);

      // Fetch products
      const { data: products, error: productsError } = await productService.getAllProducts();
      if (productsError) throw productsError;

      // Fetch orders
      const { data: orders, error: ordersError } = await orderService.getAllOrders();
      if (ordersError) throw ordersError;

      // Calculate stats
      const totalProducts = products?.length || 0;
      const activeProducts = products?.filter(p => p.is_active).length || 0;
      const lowStockProducts = products?.filter(p => (p.stock_quantity || 0) < 10).length || 0;

      const totalOrders = orders?.length || 0;
      const pendingOrders = orders?.filter(o => o.status === 'pending').length || 0;

      const totalRevenue = orders?.filter(o => o.status === 'delivered')
        .reduce((sum, o) => sum + o.total_amount, 0) || 0;

      const currentMonth = new Date().getMonth();
      const currentYear = new Date().getFullYear();
      const monthlyRevenue = orders?.filter(o => {
        const orderDate = new Date(o.created_at || '');
        return orderDate.getMonth() === currentMonth &&
               orderDate.getFullYear() === currentYear &&
               o.status === 'delivered';
      }).reduce((sum, o) => sum + o.total_amount, 0) || 0;

      setStats({
        totalProducts,
        activeProducts,
        lowStockProducts,
        totalOrders,
        pendingOrders,
        totalRevenue,
        monthlyRevenue
      });
    } catch (error: any) {
      console.error('Error fetching dashboard stats:', error);
      toast({
        title: "Error",
        description: "Failed to load dashboard statistics",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const dashboardCards = [
    {
      title: 'Total Products',
      value: loading ? '...' : stats.totalProducts.toString(),
      icon: Package,
      description: `${stats.activeProducts} active products`,
      trend: stats.lowStockProducts > 0 ? `${stats.lowStockProducts} low stock` : 'All stocked'
    },
    {
      title: 'Total Orders',
      value: loading ? '...' : stats.totalOrders.toString(),
      icon: ShoppingBag,
      description: `${stats.pendingOrders} pending orders`,
      trend: 'All time'
    },
    {
      title: 'Monthly Revenue',
      value: loading ? '...' : `$${stats.monthlyRevenue.toFixed(2)}`,
      icon: TrendingUp,
      description: 'This month',
      trend: 'Current month'
    },
    {
      title: 'Total Revenue',
      value: loading ? '...' : `$${stats.totalRevenue.toFixed(2)}`,
      icon: DollarSign,
      description: 'All time revenue',
      trend: 'Delivered orders only'
    }
  ];

  return (
    <Layout>
      <div className="container py-8">
        <div className="flex items-center justify-between mb-8">
          <h1 className="text-3xl font-bold">Admin Dashboard</h1>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {dashboardCards.map((card, index) => {
            const Icon = card.icon;
            return (
              <Card key={index} className="glass-effect">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">
                    {card.title}
                  </CardTitle>
                  <Icon className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold gradient-text">{card.value}</div>
                  <p className="text-xs text-muted-foreground">
                    {card.description}
                  </p>
                  <p className="text-xs text-muted-foreground mt-1">
                    {card.trend}
                  </p>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Alerts Section */}
        {(stats.lowStockProducts > 0 || stats.pendingOrders > 0) && (
          <div className="mb-6">
            <h2 className="text-xl font-semibold mb-4">Alerts</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {stats.lowStockProducts > 0 && (
                <Card className="border-orange-200 bg-orange-50">
                  <CardContent className="p-4">
                    <div className="flex items-center gap-2">
                      <AlertTriangle className="h-5 w-5 text-orange-600" />
                      <div>
                        <p className="font-medium text-orange-800">Low Stock Alert</p>
                        <p className="text-sm text-orange-600">
                          {stats.lowStockProducts} products have low stock (less than 10 items)
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              {stats.pendingOrders > 0 && (
                <Card className="border-blue-200 bg-blue-50">
                  <CardContent className="p-4">
                    <div className="flex items-center gap-2">
                      <ShoppingBag className="h-5 w-5 text-blue-600" />
                      <div>
                        <p className="font-medium text-blue-800">Pending Orders</p>
                        <p className="text-sm text-blue-600">
                          {stats.pendingOrders} orders are waiting to be processed
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card className="glass-effect">
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <a href="/admin/products" className="block p-4 rounded-lg hover:bg-muted/50 transition-colors">
                <h3 className="font-semibold">Manage Products</h3>
                <p className="text-sm text-muted-foreground">Add, edit, or remove products</p>
              </a>
              <a href="/admin/orders" className="block p-4 rounded-lg hover:bg-muted/50 transition-colors">
                <h3 className="font-semibold">Manage Orders</h3>
                <p className="text-sm text-muted-foreground">View and update order status</p>
              </a>
            </CardContent>
          </Card>

          <Card className="glass-effect">
            <CardHeader>
              <CardTitle>Recent Activity</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                No recent activity to display.
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    </Layout>
  );
}
