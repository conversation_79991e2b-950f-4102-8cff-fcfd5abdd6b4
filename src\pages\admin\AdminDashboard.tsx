
import React from 'react';
import { Navigate } from 'react-router-dom';
import { ShoppingBag, Package, Users, DollarSign } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useAuth } from '@/contexts/AuthContext';
import { Layout } from '@/components/layout/Layout';

export default function AdminDashboard() {
  const { profile } = useAuth();

  if (!profile?.is_admin) {
    return <Navigate to="/" replace />;
  }

  const stats = [
    {
      title: 'Total Products',
      value: '6',
      icon: Package,
      description: 'Active products in store'
    },
    {
      title: 'Total Orders',
      value: '0',
      icon: ShoppingBag,
      description: 'Orders this month'
    },
    {
      title: 'Total Customers',
      value: '1',
      icon: Users,
      description: 'Registered users'
    },
    {
      title: 'Revenue',
      value: '$0.00',
      icon: DollarSign,
      description: 'Total revenue'
    }
  ];

  return (
    <Layout>
      <div className="container py-8">
        <div className="flex items-center justify-between mb-8">
          <h1 className="text-3xl font-bold">Admin Dashboard</h1>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {stats.map((stat) => (
            <Card key={stat.title} className="glass-effect">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  {stat.title}
                </CardTitle>
                <stat.icon className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold gradient-text">{stat.value}</div>
                <p className="text-xs text-muted-foreground">
                  {stat.description}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card className="glass-effect">
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <a href="/admin/products" className="block p-4 rounded-lg hover:bg-muted/50 transition-colors">
                <h3 className="font-semibold">Manage Products</h3>
                <p className="text-sm text-muted-foreground">Add, edit, or remove products</p>
              </a>
              <a href="/admin/orders" className="block p-4 rounded-lg hover:bg-muted/50 transition-colors">
                <h3 className="font-semibold">Manage Orders</h3>
                <p className="text-sm text-muted-foreground">View and update order status</p>
              </a>
            </CardContent>
          </Card>

          <Card className="glass-effect">
            <CardHeader>
              <CardTitle>Recent Activity</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                No recent activity to display.
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    </Layout>
  );
}
