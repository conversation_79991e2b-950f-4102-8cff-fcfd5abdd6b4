
import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  <PERSON><PERSON>Header,
  DialogTitle,
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { orderService } from '@/services/orderService';
import { OrderWithItems, OrderStatus } from '@/types/order';
import { useToast } from '@/hooks/use-toast';

interface OrderDetailsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  order: OrderWithItems | null;
  onOrderUpdate: () => void;
}

const statusOptions: OrderStatus[] = ['pending', 'processing', 'shipped', 'delivered', 'cancelled'];

export function OrderDetailsDialog({ 
  open, 
  onOpenChange, 
  order, 
  onOrderUpdate 
}: OrderDetailsDialogProps) {
  const { toast } = useToast();

  if (!order) return null;

  const updateStatus = async (newStatus: OrderStatus) => {
    try {
      console.log('Updating order status from dialog:', order.id, newStatus);
      const { error } = await orderService.updateOrderStatus(order.id, newStatus);
      if (error) throw error;
      
      toast({
        title: "Success",
        description: `Order status updated to ${newStatus}`,
      });
      onOrderUpdate();
    } catch (error: any) {
      console.error('Error updating order status:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to update order status",
        variant: "destructive",
      });
    }
  };

  const cancelOrder = async () => {
    try {
      console.log('Cancelling order:', order.id);
      const { error } = await orderService.cancelOrder(order.id);
      if (error) throw error;
      
      toast({
        title: "Success",
        description: "Order cancelled successfully",
      });
      onOrderUpdate();
      onOpenChange(false);
    } catch (error: any) {
      console.error('Error cancelling order:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to cancel order",
        variant: "destructive",
      });
    }
  };

  const getStatusVariant = (status: string) => {
    switch (status) {
      case 'pending': return 'secondary';
      case 'processing': return 'outline';
      case 'shipped': return 'default';
      case 'delivered': return 'default';
      case 'cancelled': return 'destructive';
      default: return 'secondary';
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Order Details</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6">
          {/* Order Header */}
          <div className="flex justify-between items-start">
            <div>
              <p className="text-sm text-muted-foreground">Order ID</p>
              <p className="font-mono text-sm">{order.id}</p>
            </div>
            <Badge variant={getStatusVariant(order.status)}>
              {order.status}
            </Badge>
          </div>

          {/* Customer Info */}
          <div>
            <h4 className="font-semibold mb-2">Customer Information</h4>
            <div className="space-y-1">
              <p><span className="font-medium">Name:</span> {order.profiles?.full_name || order.customer_name || 'N/A'}</p>
              <p><span className="font-medium">Email:</span> {order.profiles?.email || order.customer_email}</p>
              {order.customer_phone && (
                <p><span className="font-medium">Phone:</span> {order.customer_phone}</p>
              )}
              <p><span className="font-medium">Order Date:</span> {new Date(order.created_at).toLocaleDateString()}</p>
            </div>
          </div>

          {/* Shipping Address */}
          {order.shipping_address && (
            <div>
              <h4 className="font-semibold mb-2">Shipping Address</h4>
              <div className="text-sm text-muted-foreground">
                <p>{order.shipping_address.street}</p>
                <p>{order.shipping_address.city}, {order.shipping_address.state} {order.shipping_address.zip}</p>
                <p>{order.shipping_address.country}</p>
              </div>
            </div>
          )}

          {/* Delivery Notes */}
          {order.delivery_notes && (
            <div>
              <h4 className="font-semibold mb-2">Delivery Notes</h4>
              <p className="text-sm text-muted-foreground">{order.delivery_notes}</p>
            </div>
          )}

          <Separator />

          {/* Order Items */}
          <div>
            <h4 className="font-semibold mb-3">Order Items</h4>
            <div className="space-y-3">
              {order.order_items?.map((item, index) => (
                <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <img
                      src={item.products?.image_url || '/placeholder.svg'}
                      alt={item.products?.name || 'Product'}
                      className="w-12 h-12 object-cover rounded"
                    />
                    <div>
                      <p className="font-medium">{item.products?.name || 'Unknown Product'}</p>
                      <p className="text-sm text-muted-foreground">
                        Quantity: {item.quantity} × ${(item.unit_price || item.price_at_time).toFixed(2)}
                      </p>
                    </div>
                  </div>
                  <p className="font-medium">${(item.total_price || (item.price_at_time * item.quantity)).toFixed(2)}</p>
                </div>
              ))}
            </div>
          </div>

          <Separator />

          {/* Order Total */}
          <div className="flex justify-between items-center">
            <span className="font-semibold">Total Amount:</span>
            <span className="text-xl font-bold">${order.total_amount.toFixed(2)}</span>
          </div>

          {/* Status Management */}
          <div>
            <h4 className="font-semibold mb-2">Update Status</h4>
            <Select value={order.status} onValueChange={updateStatus}>
              <SelectTrigger className="w-full">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {statusOptions.map((status) => (
                  <SelectItem key={status} value={status}>
                    {status.charAt(0).toUpperCase() + status.slice(1)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-2 pt-4">
            {order.status !== 'cancelled' && order.status !== 'delivered' && (
              <Button 
                variant="destructive" 
                onClick={cancelOrder}
              >
                Cancel Order
              </Button>
            )}
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              Close
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
