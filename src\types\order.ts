
export interface Order {
  id: string;
  user_id: string | null;
  total_amount: number;
  status: string | null;
  stripe_payment_intent_id?: string | null;
  delivery_notes?: string | null;
  created_at: string | null;
  updated_at: string | null;
}

export interface OrderItem {
  id: string;
  order_id: string | null;
  product_id: string | null;
  quantity: number;
  price_at_time: number;
  created_at: string | null;
  products?: {
    id: string;
    name: string;
    image_url?: string;
    price: number;
  };
}

export interface OrderWithItems extends Order {
  order_items?: OrderItem[];
  profiles?: {
    email: string;
    full_name?: string;
  };
}

export type OrderStatus = 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled';

// Additional interfaces for admin functionality
export interface CreateOrderData {
  user_id: string;
  total_amount: number;
  status?: OrderStatus;
  delivery_notes?: string;
  order_items: {
    product_id: string;
    quantity: number;
    price_at_time: number;
  }[];
}

export interface UpdateOrderData {
  status?: OrderStatus;
  delivery_notes?: string;
  updated_at?: string;
}
